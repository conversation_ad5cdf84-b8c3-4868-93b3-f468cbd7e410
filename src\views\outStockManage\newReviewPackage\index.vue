<template>
  <div class="new-review-package-container">
    <!-- 顶部输入区域 -->
    <div class="top-input-section">
      <div class="input-row">
        <div class="input-item">
          <label>销售单号</label>
          <el-input
            ref="erpOrderCode"
            v-model="formRight.erpOrderCode"
            placeholder="请输入销售单号或扫码"
            :disabled="isSearchErp"
            @keyup.enter.native="erpOrderCodeFunction"
          />
          <el-button type="primary" @click="searchBtn">查看任务</el-button>
          <el-button type="danger" @click="refreshPage">刷新</el-button>
        </div>

        <div class="status-display">
          <span class="status-label">随货同行数量:</span>
          <span class="status-value highlight">{{ formRight.orderNumberAll || 2 }}</span>
          <span class="status-label">未打:</span>
          <span class="status-value error">{{ formRight.orderNumberCancelled || 1 }}</span>
        </div>

        <div class="delivery-type-display">
          <div class="type-item">
            <span class="type-label">快递类型:</span>
            <span class="type-value">{{ getDeliveryType() }}</span>
          </div>
          <div class="type-item">
            <span class="type-label">订单类型:</span>
            <span class="type-value">{{ getOrderType() }}</span>
          </div>
        </div>
      </div>

      <div class="input-row">
        <div class="input-item">
          <label>耗材码</label>
          <el-input
            ref="boxCode"
            v-model="formRight.boxCode"
            placeholder="请输入耗材码"
            @keyup.enter.native="boxCodeFunction"
          />
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content-area">
      <!-- 左侧商品图片和信息 -->
      <div class="left-product-section">
        <div class="product-image-box">
          <el-image
            :src="formData.src"
            fit="contain"
            class="product-image"
          >
            <div slot="error" class="image-placeholder">
              <div class="placeholder-content">
                <div class="placeholder-text">商品图片</div>
              </div>
            </div>
          </el-image>
        </div>

        <div class="product-info-list">
          <div class="info-item">
            <span class="info-label">商品条码:</span>
            <span class="info-value">{{ formData.barCode || '69010193485576' }}</span>
            <span class="info-extra">数量</span>
          </div>
          <div class="info-item">
            <span class="info-label">商品名称:</span>
            <span class="info-value">{{ formData.productName || '三九感冒灵颗粒 10袋/盒' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">商品批号:</span>
            <span class="info-value">{{ formData.batchNumber || 'Y1003020' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">包装单位:</span>
            <span class="info-value">{{ formData.packingUnit || '盒' }}</span>
          </div>
        </div>
      </div>

      <!-- 右侧商品表格 -->
      <div class="right-table-section">
        <div class="table-container">
          <vxe-table
            ref="reviewTable"
            :data="tableData"
            :loading="loading"
            height="350"
            border
            stripe
            :cell-class-name="cellClassName"
            @cell-click="cellClickRow"
          >
            <vxe-table-column field="productCode" title="商品条码" width="140" />
            <vxe-table-column field="productName" title="商品名称" width="180" />
            <vxe-table-column field="batchNumber" title="商品批号" width="120" />
            <vxe-table-column field="reviewNumber" title="商品数量(应收数量)" width="160" />
            <vxe-table-column field="reviewNumberPieces" title="已装数" width="80" />
            <vxe-table-column field="specification" title="未装数" width="80" />
            <vxe-table-column field="reviewStatus" title="装箱数量" width="100" />
            <vxe-table-column title="操作" width="80" fixed="right">
              <template v-slot="{ row }">
                <el-button
                  v-if="row.reviewStatus === 0"
                  type="primary"
                  size="mini"
                  @click="reviewClick(row)"
                >
                  复核
                </el-button>
                <el-button
                  v-else
                  type="success"
                  size="mini"
                  @click="unReview(row)"
                >
                  取消复核
                </el-button>
              </template>
            </vxe-table-column>
          </vxe-table>
        </div>

        <!-- 表格下方按钮区 -->
        <div class="table-action-buttons">
          <div class="button-group">
            <el-button type="primary" @click="reviewConfirmAction">复核确认</el-button>
            <el-button type="warning" @click="showExceptionSubmissionDialog">异常提交</el-button>
            <el-button type="info" @click="changeCYS">更换承运商</el-button>
          </div>

          <!-- 快捷键输入区 -->
          <div class="shortcut-input-area">
            <div class="shortcut-item">
              <span class="shortcut-label">耗材编码</span>
              <el-input placeholder="H01" size="small" />
            </div>
            <div class="shortcut-item">
              <span class="shortcut-label">耗材名称</span>
              <el-input placeholder="二手箱" size="small" />
            </div>
            <div class="shortcut-item">
              <span class="shortcut-label">操作</span>
              <el-input placeholder="删除" size="small" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部统计信息 -->
    <div class="bottom-section">
      <div class="stats-container">
        <div class="stat-item">
          <span class="stat-label">已装箱商品总数</span>
          <span class="stat-value primary">{{ formRight.productNumber || 10 }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">未装箱商品总数</span>
          <span class="stat-value danger">{{ (formRight.productNumberAll - formRight.productNumber) || 250 }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">商品总数</span>
          <span class="stat-value">{{ formRight.productNumberAll || 260 }}</span>
        </div>
      </div>

      <!-- 耗材信息表格 -->
      <div class="material-table-container">
        <vxe-table
          ref="materialTable"
          :data="boxTableData"
          height="120"
          border
        >
          <vxe-table-column field="boxCode" title="耗材条码" />
          <vxe-table-column field="boxType" title="耗材名称" />
          <vxe-table-column field="specification" title="已装箱数量" />
          <vxe-table-column field="capacity" title="未装箱数量" />
          <vxe-table-column field="category" title="耗材类别" />
          <vxe-table-column field="description" title="耗材描述" />
        </vxe-table>
      </div>
    </div>

    <!-- 弹窗组件 -->
    <tracing-code-scan
      ref="tracingCodeScanDialog"
      @on-before-close="closeDialog"
      @regulatoryCodeScanBack="changeTracing"
    />
    <view-tasks
      ref="viewTasksDialog"
      @on-before-close="closeViewTasksDialog"
    />
    <exception-submission
      ref="exceptionSubmitDialog"
      @on-before-close="closeExceptionSubmitDialog"
      @inputOpenDialog="openGoodsDialog"
      @deleteRow="deleteRow"
    />
    <goods-dialog
      ref="goodsDialog"
      @checkGoodsData="checkGoodsData"
    />
    <change-cys-alert-new
      ref="dialogChangeCYSAlert"
    />
  </div>
</template>

<script>
import { doTTS } from "@/utils/tts.js";
import tracingCodeScan from "./components/tracingCodeScan";
import ViewTasks from "./components/viewTasks";
import ExceptionSubmission from "./components/exceptionSubmission";
import GoodsDialog from "./components/goodsDialog";
import ChangeCysAlertNew from "./components/changeCYSAlertNew";

// 复用原有API
import {
  getPartsInReviewGoodsSalesInfo,
  listPartsInReviewGoodsView,
  review,
  reviewConfirm,
  videoStart,
  videoPing,
  getMergeOrderList,
  deleteMergeOrder,
  checkParts,
  changeCarrier
} from "@/api/outstock/fhdb";
import XEUtils from "xe-utils";
import Axios from "axios";
import { printNew } from '../../../utils/print';

export default {
  name: "NewReviewPackage",
  components: {
    tracingCodeScan,
    ViewTasks,
    ExceptionSubmission,
    GoodsDialog,
    ChangeCysAlertNew
  },
  data() {
    return {
      // 复用原有数据结构
      boxLoading: false,
      intervalId: undefined,
      mainLoading: false,
      isSearchErp: false,

      // 表单数据
      formData: {
        productName: "",
        specifications: "",
        batchNumber: "",
        produceTime: "",
        validDate: "",
        reviewNumber: "",
        packingUnit: "",
        storageAttributes: "",
        manufacturer: "",
        packagingRequirement: "",
        mediumPacking: "",
        storageAttributesExt: "",
        src: require("@/assets/images/product.png"),
        di: '',
        barCode: ""
      },

      formRight: {
        allocationCode: "",
        erpOrderCode: "",
        boxCode: "",
        consolidationCode: undefined,
        orderCode: "",
        barCode: "",
        productNumberAll: 0,
        productNumber: 0,
        inreviewProductNumberAll: 0,
        inreviewProductNumber: 0,
        supervisoryCodeAcquisition: "",
        orderNumberAll: 0,
        orderNumber: 0,
        consolidationNumber: 0,
        orderNumberCancelled: 0,
        carrierName: "",
      },

      checkRow: undefined,
      isMajorClients: false,
      checkCancelOrder: 1,
      hotkeys: "F1,F2,F4,F7,F10",
      tableData: [],
      boxTableData: [],
      hasDialog: false,
      loading: false,
      mergeOrderCode: "",
      addBoxCodeOpen: false,
      videoId: "-1",
      formRightArr: [],
    };
  },

  activated() {
    this.$refs.erpOrderCode.focus();
  },

  created() {
    this.loading = true;
    setTimeout(() => {
      this.loading = false;
    }, 1000);

    // 绑定快捷键
    this.$hotkeys(this.hotkeys, (event, handler) => {
      if (this.hasDialog) {
        return false;
      }
      event.preventDefault();
      switch (handler.key) {
        case "F1":
          this.showExceptionSubmissionDialog();
          break;
        case "F2":
          this.refreshPage();
          break;
        case "F4":
          this.searchBtn();
          break;
        case "F7":
          this.reviewConfirmAction();
          break;
        case "F10":
          this.changeCYS();
          break;
      }
    });
  },

  beforeDestroy() {
    this.$hotkeys.unbind(this.hotkeys);
    clearInterval(this.intervalId);
  },

  methods: {
    // 获取快递类型
    getDeliveryType() {
      return "顺丰(易碎品)";
    },

    // 获取订单类型
    getOrderType() {
      return "京东";
    },

    // 复用原有方法
    refreshPage() {
      this.clearPage();
    },

    clearPage() {
      this.isMajorClients = false;
      this.isSearchErp = false;
      this.formRight = {
        allocationCode: "",
        erpOrderCode: "",
        boxCode: "",
        consolidationCode: undefined,
        orderCode: "",
        barCode: "",
        productNumberAll: 0,
        productNumber: 0,
        inreviewProductNumberAll: 0,
        inreviewProductNumber: 0,
        orderNumber: "",
        orderNumberAll: "",
        consolidationNumber: 0,
        orderNumberCancelled: 0,
      };
      this.formData = {
        productName: "",
        specifications: "",
        batchNumber: "",
        produceTime: "",
        validDate: "",
        reviewNumber: "",
        packingUnit: "",
        storageAttributes: "",
        manufacturer: "",
        packagingRequirement: "",
        mediumPacking: "",
        storageAttributesExt: "",
        src: require("@/assets/images/product.png"),
        di: '',
        barCode: ""
      };
      this.formRightArr = [];
      this.tableData = [];
      this.boxTableData = [];
      this.$nextTick(() => {
        this.$refs.erpOrderCode.focus();
      });
    },

    // 销售单号回车事件
    erpOrderCodeFunction() {
      this.apigetPartsInReviewGoodsSalesInfo();
    },

    // 耗材码回车事件
    boxCodeFunction() {
      this.apiCheckBoxCode();
    },

    // 商品条码回车事件
    barCodeEnter() {
      const barCode = this.formRight.barCode;
      const goodsList = [];
      let rowobj = {};
      let offon = 0;

      const table_data = this.tableData;
      const barCodeGoods = this.tableData.filter((item) => (barCode === item.di || item.barCode === barCode));

      // 检查重复条码
      const barCodeMap = new Map();
      const duplicateBarCodes = new Set();

      barCodeGoods.forEach(product => {
        const { barCode, productCode } = product;
        if (!barCodeMap.has(barCode)) {
          barCodeMap.set(barCode, new Set([productCode]));
        } else {
          const codes = barCodeMap.get(barCode);
          codes.add(productCode);
          if (codes.size > 1) {
            duplicateBarCodes.add(barCode);
          }
        }
      });

      if (duplicateBarCodes.size > 0 && this.tableData.length > 0) {
        this.$nextTick(() => {
          this.$alert('商品有多个编码，请手动点击复核', '提示', {
            confirmButtonText: '确定',
            type: 'warning',
          }).then(() => {
            return true;
          });
        });
      } else {
        for (var i = 0; i < table_data.length; i++) {
          const obj = table_data[i];
          var barCodeArr = obj.barCode ? obj.barCode.split(",") : [];

          for (var j = 0; j < barCodeArr.length; j++) {
            if (barCode === barCodeArr[j] || barCode === table_data[i].di) {
              if (obj.reviewStatus === 2) {
                this.setReviewDetails(obj);
                this.$message.warning("该条形码对应的商品已复核！");
                this.$refs.barCode.select();
                this.dottsFunction("商品已复核");
                return;
              }

              if (obj.whetherRegulatory === 1) {
                if (obj.reviewNumber > 0) {
                  offon = 1;
                  goodsList.push(obj);
                }
              } else {
                this.$refs.barCode.select();
                rowobj = obj;
              }
            }
          }
        }

        if (Object.keys(rowobj).length !== 0) {
          this.setReviewDetails(rowobj);
        }

        if (offon === 1) {
          let obj = goodsList[0];
          this.setReviewDetails(obj);
          const params = {
            allocationCode: obj.allocationCode,
            productCode: obj.productCode,
            goods: goodsList,
            orderCode: obj.orderCode,
            mergeOrderCode: this.mergeOrderCode,
          };
          this.$refs.tracingCodeScanDialog.open(params);
          this.hasDialog = true;
          return;
        }

        if (Object.keys(rowobj).length !== 0) {
          this.checkAndPosition(rowobj, true);
          return;
        } else {
          this.checkAndPosition();
        }
      }

      this.$refs.formData?.resetFields();
      this.formData.packingUnit = "";
      this.formData.src = require("@/assets/images/product.png");
    },

    // 双击选中行并定位 \ 选中相同商品(同编码)
    checkAndPosition(obj, needTTS) {
      const params = {
        productCode: obj ? obj.productCode : this.formRight.barCode,
        mergeOrderCode: this.mergeOrderCode,
      };

      review(params).then((res) => {
        const { code, msg, result } = res;
        this.setRowReviewStates();
        if (code === 0) {
          this.setRightNumber(result);
          this.$message.success(msg);
          this.apilistPartsInReviewGoodsView();
          if (needTTS) {
            const tableRow = this.tableData;
            let num = 0;
            tableRow.forEach((i, v) => {
              if (params.productCode === i.productCode) {
                num += i.reviewNumber;
              }
            });
            this.dottsFunction(num + obj.packingUnit);
          }
        } else {
          this.$message.error(msg);
        }
      });
    },

    // 查看任务
    searchBtn() {
      this.$refs.viewTasksDialog.open();
      this.hasDialog = true;
    },

    // 复核确认
    reviewConfirmAction() {
      if (this.boxTableData.length === 0) {
        this.$message.error("请先输入耗材码后再进行操作");
        return;
      }
      this.apireviewConfirm();
    },

    // 异常提交
    showExceptionSubmissionDialog() {
      if (this.tableData.length === 0) {
        this.$message.warning("请先扫描销售单号获取商品列表");
        return;
      }
      this.$refs.exceptionSubmitDialog.open(this.tableData, this.mergeOrderCode);
      this.hasDialog = true;
    },

    // 承运商更换
    changeCYS() {
      if (this.formRight.erpOrderCode === "") {
        this.$message.error("请输入销售单号");
        return;
      }
      changeCarrier({ orderCode: this.formRight.erpOrderCode }).then(res => {
        const { code, msg, result } = res;
        if (code === 0 && result) {
          this.$refs.dialogChangeCYSAlert.open(this.formRight.erpOrderCode, result);
        } else {
          this.$message.error(msg);
        }
      });
    },

    // 表格行点击事件
    cellClickRow({ rowIndex, row }) {
      this.checkRow = row;
      this.formData = {
        productName: row.productName,
        batchNumber: row.batchNumber,
        produceTime: row.produceTime,
        validDate: row.validDate,
        specifications: row.specifications,
        reviewNumber: row.reviewNumber,
        packingUnit: row.packingUnit,
        storageAttributes: row.storageAttributes,
        manufacturer: row.manufacturer,
        packagingRequirement: row.packagingRequirement,
        mediumPacking: row.mediumPacking,
        storageAttributesExt: row.storageAttributesExt,
        productGiftsNumber: row.productGiftsNumber,
        productGiftsName: row.productGiftsName,
        giftsPackingUnit: row.giftsPackingUnit,
        src: row.productPic ? row.productPic[0] : require("@/assets/images/product.png"),
        di: row.di,
        barCode: row.barCode
      };
      this.setReviewDetails(row, true);
    },

    // 复核按钮点击
    reviewClick(row) {
      const { reviewStatus, whetherRegulatory } = row;
      if (reviewStatus === 2) {
        this.unReview(row);
      } else if (whetherRegulatory === 1 && row.reviewNumber != 0) {
        this.whetherRegulatory(row);
      } else {
        this.checkAndPosition1(row);
      }
    },

    // 取消复核
    unReview(row) {
      this.apireview(row.allocationCode, row.productCode, 1, row.orderCode);
    },

    // 表格行样式
    cellClassName({ row, column }) {
      let rowName = "";
      if (row.reviewStatus == 2) {
        rowName = "cell-green";
      } else if (row.exceptionStatus !== null) {
        rowName = "cell-red";
      } else {
        rowName = "cell-white";
      }
      if (this.checkRow) {
        rowName = this.checkRow.barCode === row.barCode ? "cell-blue" : rowName;
      }
      return rowName;
    },

    // 弹窗关闭事件
    closeDialog() {
      this.hasDialog = false;
    },

    closeViewTasksDialog() {
      this.hasDialog = false;
      this.$refs.erpOrderCode.focus();
    },

    closeExceptionSubmitDialog() {
      this.hasDialog = false;
      this.isSearchErp = false;
      this.$refs.erpOrderCode.focus();
      this.apilistPartsInReviewGoodsView();
    },

    // 商品弹窗相关
    openGoodsDialog(params) {
      this.$refs.goodsDialog.open(params);
    },

    checkGoodsData(params) {
      this.$refs.exceptionSubmitDialog.receiveGoodsData(params);
    },

    deleteRow(params) {
      this.$refs.goodsDialog.overWriteGoodsData(params);
    },

    // 追溯码扫描成功后
    changeTracing(result) {
      this.setRightNumber(result);
      this.apilistPartsInReviewGoodsView();
    },

    // API调用方法
    // 销售单号搜索
    apigetPartsInReviewGoodsSalesInfo() {
      getPartsInReviewGoodsSalesInfo({
        erpOrderCode: this.formRight.erpOrderCode,
      }).then((res) => {
        const { code, msg, result } = res;
        if (code === 0) {
          if (result.wholeFlag === true) {
            this.$alert('该复核任务有整件任务', '提示', {
              confirmButtonText: '确定',
              type: 'warning',
            }).then(() => {
              this.processSearchResult(result);
            });
          } else {
            this.processSearchResult(result);
          }
        } else {
          this.$refs.erpOrderCode.focus();
          this.clearPage();
          this.$message.error(msg);
        }
      });
    },

    // 处理搜索结果
    processSearchResult(result) {
      this.formRight.inreviewProductNumber = result.inreviewProductNumber;
      this.formRight.consolidationCode = result.consolidationCode;
      this.formRight.orderCode = result.orderCode;
      this.formRight.allocationCode = result.allocationCode;
      this.formRight.supervisoryCodeAcquisition = result.supervisoryCodeAcquisition;
      this.formRight.orderNumberCancelled = result.orderNumberCancelled;
      this.formRight.carrierName = result.carrierName;
      this.isMajorClients = result.isMajorClients;
      this.mergeOrderCode = result.mergeOrderCode;
      this.setRightNumber(result);
      this.apivideoStart(result.mergeOrderCode, result.isVideoEnabled);
      this.apilistPartsInReviewGoodsView();
      this.isSearchErp = true;
    },

    // 获取复核商品列表
    apilistPartsInReviewGoodsView() {
      let reviewNum = 0;
      listPartsInReviewGoodsView({
        orderCode: this.formRight.orderCode,
        allocationCode: this.formRight.allocationCode,
        mergeOrderCode: this.mergeOrderCode,
      }).then((res) => {
        const { code, msg, result } = res;
        if (code === 0 && result) {
          const data = JSON.parse(JSON.stringify(result));
          data.forEach(item => {
            item.rowId = this.genID();
            item.erpOrderCode = this.formRight.erpOrderCode;
            item.orderCode = item.mergeOrderCode;
            item.reviewNumberPieces = item.reviewNumber / item.specification;
            if (item.reviewStatus == 2) {
              reviewNum++;
            }
            if (item.productPic == null) {
              item.productPic = [];
            }
          });
          this.tableData = data;

          if (this.tableData.length === reviewNum && this.formRightArr[2]?.totalValue == this.formRightArr[2]?.checkedValue) {
            this.$refs.boxCode.select();
          } else {
            this.$refs.barCode?.select();
          }
        } else {
          this.$message.error(msg);
        }
      });
    },

    // 校验耗材码
    apiCheckBoxCode() {
      const params = {
        boxCode: this.formRight.boxCode,
      };
      checkParts(params).then(res => {
        const { code, msg, result } = res;
        if (code === 0 && result) {
          this.$message.success(msg);
          this.boxTableData.push(result);
          this.formRight.boxCode = '';
          this.apilistPartsInReviewGoodsView();
          this.formRight.consolidationNumber = this.boxTableData.length;
        } else {
          this.formRight.boxCode = '';
          this.$message.error(msg);
        }
      });
    },

    // 复核确认
    apireviewConfirm() {
      this.mainLoading = true;
      reviewConfirm({
        orderCode: this.formRight.orderCode,
        mergeOrderCode: this.mergeOrderCode,
        checkCancelOrder: this.checkCancelOrder === 1 ? undefined : 0,
        boxCodeList: this.boxTableData.map(item => item.boxCode)
      }).then((res) => {
        this.mainLoading = false;
        const { code, msg, result } = res;
        this.checkCancelOrder = 1;
        if (code === 0) {
          if (result.isPrint == 1) {
            printNew(result.printContent);
          }
          this.clearPage();
          this.$message.success(msg);
          this.isSearchErp = false;
          this.$nextTick(() => {
            this.$refs.erpOrderCode.focus();
          });
        } else if (code === 2000) {
          this.$confirm(msg, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            this.checkCancelOrder = 0;
            this.apireviewConfirm();
          }).catch(() => {
            this.checkCancelOrder = 0;
          });
        } else {
          this.$message.error(msg);
        }
      });
    },

    // 生成ID
    genID() {
      const length = 10;
      return Number(Math.random().toString().substr(3, length) + Date.now()).toString(36);
    },

    // 设置右侧统计数据
    setRightNumber(result) {
      this.formRightArr = [];
      this.formRight.productNumberAll = result.productNumberAll;
      this.formRight.productNumber = result.productNumber;
      this.formRightArr.push({
        name: '商品总数量',
        totalValue: result.productNumberAll,
        checkedValue: result.productNumber
      });
      this.formRight.inreviewProductNumberAll = result.inreviewProductNumberAll;
      this.formRight.inreviewProductNumber = result.inreviewProductNumber;
      this.formRightArr.push({
        name: '品种数',
        totalValue: result.inreviewProductNumberAll,
        checkedValue: result.inreviewProductNumber
      });
      this.formRight.orderNumberAll = result.orderNumberAll;
      this.formRight.orderNumber = result.orderNumber;
      this.formRightArr.push({
        name: '随货同行数',
        totalValue: result.orderNumberAll,
        checkedValue: result.orderNumber
      });
    },

    // 复核操作
    apireview(allocationCode, productCode, cancel, orderCode) {
      review({
        allocationCode: allocationCode,
        productCode: productCode,
        orderCode: orderCode,
        cancel: cancel,
        mergeOrderCode: this.mergeOrderCode,
      }).then((res) => {
        const { code, msg, result } = res;
        this.setRowReviewStates();
        if (code === 0) {
          this.apilistPartsInReviewGoodsView();
          this.setRightNumber(result);
          this.$message.success(msg);
        } else {
          this.formRight.barCode = "";
          this.$message.error(msg);
        }
      });
    },

    // 选中相同商品复核
    checkAndPosition1(row) {
      const { allocationCode, productCode, orderCode } = row;
      if (productCode === "") {
        return;
      }

      const tableRow = this.tableData;
      const ids = [];
      let num = 0;
      tableRow.forEach((i, v) => {
        if (productCode === i.productCode) {
          this.$refs.reviewTable.setCheckboxRow(i, true);
          const id = i.id;
          num += i.reviewNumber;
          ids.push(id);
        }
      });

      const params = {
        productCode: productCode,
        orderCode: orderCode,
        mergeOrderCode: this.mergeOrderCode,
      };

      review(params).then((res) => {
        const { code, msg, result } = res;
        this.setRowReviewStates();
        if (code === 0) {
          this.$message.success(msg);
          this.setRightNumber(result);
          this.apilistPartsInReviewGoodsView();
          this.dottsFunction(num + row.packingUnit);
        } else {
          this.$message.error(msg);
        }
      });

      if (ids.length) {
        this.scrollTop(row, productCode);
      }
    },

    // 是否展示电子监管码弹窗
    whetherRegulatory(row) {
      const { allocationCode, productCode, whetherRegulatory, orderCode, largeCategoryCode } = row;
      const goodsList = [];
      this.tableData.forEach((res) => {
        if (res.productCode === productCode) {
          if (
            res.exceptionCause != null &&
            res.exceptionReason != undefined &&
            res.exceptionNumber != null
          ) {
            res.reviewStatus = 0;
            return false;
          }
          if (res.reviewNumber != 0) {
            goodsList.push(res);
          }
        }
      });

      if (whetherRegulatory === 1) {
        if (goodsList.length === 0) {
          this.$message.warning("请获取需要扫描电子监管码的商品！");
          return;
        }
        const obj = {
          allocationCode: allocationCode,
          productCode: productCode,
          goods: goodsList,
          orderCode: orderCode,
          mergeOrderCode: this.mergeOrderCode,
          largeCategoryCode: largeCategoryCode,
        };
        this.$refs.tracingCodeScanDialog.open(obj);
        this.hasDialog = true;
      }
    },

    // 设置展示商品主信息
    setReviewDetails(obj) {
      const tableData = this.tableData;
      this.formData.src = obj.productPic[0] ? obj.productPic[0] : require("@/assets/images/product.png");

      Object.assign(this.formData, obj);
      this.formData.src = obj.productPic[0] ? obj.productPic[0] : require("@/assets/images/product.png");

      let realPickingNumber = 0;
      const batchNumberList = [];
      const produceTimeList = [];
      const validDateList = [];

      const batchNumberMap = {};
      const produceTimeMap = {};
      const validDateMap = {};

      for (let i = 0; i < tableData.length; i++) {
        const d = tableData[i];
        if (d.productCode === obj.productCode) {
          const batchNumber = d.batchNumber;
          if (batchNumberMap[batchNumber]) {
            batchNumberMap[batchNumber] = parseInt(batchNumberMap[batchNumber]) + parseInt(d.reviewNumber);
          } else {
            batchNumberMap[batchNumber] = d.reviewNumber;
            produceTimeMap[batchNumber] = d.produceTime;
            validDateMap[batchNumber] = d.validDate;
          }
          realPickingNumber += parseInt(d.reviewNumber);
        }
      }

      let ll = 0;
      let batchNumberMapLength = 0;
      for (const key in batchNumberMap) {
        batchNumberMapLength++;
      }

      for (const key in batchNumberMap) {
        const batchNumber = key;
        const reviewNumber = batchNumberMap[batchNumber];
        const produceTime = produceTimeMap[batchNumber];
        const validDate = validDateMap[batchNumber];

        if (ll === batchNumberMapLength - 1) {
          batchNumberList.push(batchNumber + "(" + reviewNumber + ")");
          produceTimeList.push(produceTime);
          validDateList.push(validDate);
        } else {
          batchNumberList.push(batchNumber + "(" + reviewNumber + ")");
          produceTimeList.push(produceTime);
          validDateList.push(validDate);
        }
        ll++;
      }

      const batchNum = batchNumberList.toString().replace('"', "");
      this.formData.batchNumber = batchNum;

      const produceTime1 = produceTimeList.toString();
      if (produceTimeList.length > 1) {
        this.formData.produceTime = produceTimeList
          .map((item) => {
            return XEUtils.toDateString(item, "yyyy-MM-dd");
          })
          .join(",")
          .replace("[]", "");
      } else {
        this.formData.produceTime = XEUtils.toDateString(produceTime1, "yyyy-MM-dd");
      }

      const validDate1 = validDateList.toString();
      if (validDateList.length > 1) {
        this.formData.validDate = validDateList
          .map((item) => {
            return XEUtils.toDateString(item, "yyyy-MM-dd");
          })
          .join(",")
          .replace("[]", "");
      } else {
        this.formData.validDate = XEUtils.toDateString(validDate1, "yyyy-MM-dd");
      }

      this.formData.reviewNumber = realPickingNumber;
    },

    // 设置行复核状态
    setRowReviewStates() {
      // 实现行状态更新逻辑
    },

    // 滚动到指定行
    scrollTop(row, column) {
      this.$refs.reviewTable.scrollToRow(row, column);
    },

    // TTS语音播报
    dottsFunction(text) {
      if (doTTS && typeof doTTS === 'function') {
        doTTS(text);
      }
    },

    // 开启摄像头
    apivideoStart(mergeOrderCode, isVideoEnabled) {
      const options = {
        method: "POST",
        url: "http://127.0.0.1:9095/video/start?bizCode=" + mergeOrderCode,
      };
      Axios(options).then((res) => {
        const { code, msg, result } = res.data;
        if (code === 0) {
          this.videoId = result;
          clearInterval(this.intervalId);
          this.apivideoPing();
        } else {
          this.$message.error(msg || "摄像头开启失败");
          if (isVideoEnabled === "1") {
            setTimeout(() => {
              this.apivideoStart(mergeOrderCode, isVideoEnabled);
            }, 1000);
          }
        }
      }).catch((err) => {
        if (isVideoEnabled !== "1") {
          return;
        }
        this.$alert('请打开摄像头后点击重试?', '提示', {
          confirmButtonText: '重试',
          type: 'warning',
          callback: () => {
            this.apivideoStart(mergeOrderCode, isVideoEnabled);
          }
        });
      });
    },

    // 维持摄像头心跳
    apivideoPing() {
      this.intervalId = setInterval(() => {
        const options = {
          method: "POST",
          url: "http://127.0.0.1:9095/video/ping?videoId=" + this.videoId,
        };

        Axios(options).then((res) => {
          const { code, msg, result } = res.data;
          if (code !== 0) {
            this.$message.error(msg || "无法连接视频监控客户端");
          }
        });
      }, 5000);
    }
  }
};
</script>

<style lang="scss" scoped>
.new-review-package-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  // 顶部操作区
  .top-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .input-section {
      display: flex;
      align-items: center;
      gap: 20px;
      flex-wrap: wrap;

      .input-group {
        display: flex;
        align-items: center;
        gap: 8px;

        label {
          font-weight: 500;
          color: #333;
          white-space: nowrap;
        }

        .sales-input, .material-input, .barcode-input {
          width: 200px;
        }
      }

      .status-info {
        display: flex;
        gap: 15px;

        .status-item {
          .label {
            color: #666;
            font-size: 14px;
          }

          .value {
            font-weight: bold;
            margin-left: 5px;

            &.highlight {
              color: #409eff;
              font-size: 18px;
            }

            &.error {
              color: #f56c6c;
              font-size: 18px;
            }
          }
        }
      }

      .action-buttons {
        display: flex;
        gap: 10px;
      }

      .delivery-info {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-left: auto;

        .delivery-type, .order-type {
          .label {
            color: #666;
            font-size: 14px;
          }

          .value {
            font-weight: bold;
            margin-left: 5px;
            color: #333;
          }
        }
      }
    }
  }

  // 主要内容区
  .main-content {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;

    // 左侧商品图片区
    .left-section {
      width: 400px;
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .product-image-container {
        margin-bottom: 20px;

        .image-placeholder {
          width: 100%;
          height: 300px;
          border: 2px dashed #ddd;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 8px;
          background-color: #fafafa;

          .product-image {
            width: 100%;
            height: 100%;
          }

          .image-loading, .image-error {
            text-align: center;
            color: #999;
            font-size: 16px;

            .loading-text {
              font-size: 12px;
            }

            i {
              font-size: 48px;
              display: block;
              margin-bottom: 10px;
            }
          }
        }
      }

      .product-basic-info {
        .info-row {
          display: flex;
          margin-bottom: 12px;
          align-items: center;

          .info-label {
            width: 80px;
            color: #666;
            font-size: 14px;
            flex-shrink: 0;
          }

          .info-value {
            color: #333;
            font-weight: 500;
            word-break: break-all;
          }
        }
      }
    }

    // 右侧商品列表区
    .right-section {
      flex: 1;
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .product-table-container {
        margin-bottom: 20px;
      }

      .bottom-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .action-group {
          display: flex;
          gap: 10px;
        }

        .shortcut-hints {
          .hint-group {
            display: flex;
            gap: 15px;

            .hint-item {
              display: flex;
              align-items: center;
              gap: 5px;
              font-size: 12px;
              color: #666;

              .key {
                background: #f0f0f0;
                padding: 2px 6px;
                border-radius: 3px;
                font-weight: bold;
                color: #333;
              }
            }
          }
        }
      }
    }
  }

  // 底部统计信息
  .bottom-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .stats-container {
      display: flex;
      gap: 40px;
      margin-bottom: 20px;
      justify-content: center;

      .stat-item {
        text-align: center;

        .stat-label {
          display: block;
          color: #666;
          font-size: 14px;
          margin-bottom: 8px;
        }

        .stat-value {
          display: block;
          font-size: 24px;
          font-weight: bold;
          color: #333;

          &.primary {
            color: #409eff;
          }

          &.danger {
            color: #f56c6c;
          }
        }
      }
    }

    .material-table-container {
      border: 1px solid #ebeef5;
      border-radius: 4px;
    }
  }
}

// 表格行样式
::v-deep .vxe-table {
  .cell-green {
    background-color: #f0f9ff !important;
    color: #67c23a;
  }

  .cell-red {
    background-color: #fef0f0 !important;
    color: #f56c6c;
  }

  .cell-blue {
    background-color: #ecf5ff !important;
    color: #409eff;
  }

  .cell-white {
    background-color: #ffffff !important;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .new-review-package-container {
    .main-content {
      flex-direction: column;

      .left-section {
        width: 100%;
      }
    }

    .top-section .input-section {
      .delivery-info {
        margin-left: 0;
        margin-top: 10px;
      }
    }
  }
}
</style>